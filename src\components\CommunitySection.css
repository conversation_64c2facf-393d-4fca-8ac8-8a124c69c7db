/* Community Section Styles - Future-Proof Design */
.community-section {
  background: linear-gradient(135deg, var(--color-heritage-emerald) 0%, var(--color-heritage-sapphire) 100%);
  color: white;
  padding: var(--space-2xl) var(--space-lg);
  position: relative;
}

/* Smooth transition gradient at top of Community section */
.community-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(180deg,
    rgba(248, 246, 241, 0.3) 0%,
    rgba(248, 246, 241, 0.1) 50%,
    transparent 100%
  );
  pointer-events: none;
  z-index: 1;
}

.community-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.community-container h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 var(--space-2xl) 0;
  color: var(--color-heritage-gold);
}

/* Launch Mode Styles */
.launch-mode .launch-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2xl);
  align-items: start;
  margin-bottom: var(--space-2xl);
}

.launch-message {
  text-align: left;
}

.launch-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
}

.launch-message h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0 0 var(--space-lg) 0;
  color: var(--color-heritage-gold);
}

.launch-message p {
  font-size: var(--text-lg);
  line-height: 1.7;
  opacity: 0.95;
  margin: 0;
}

.launch-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.launch-feature {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  text-align: left;
  background: rgba(255, 255, 255, 0.12);
  padding: var(--space-lg);
  border-radius: 25px; /* Pill-shaped for consistency */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.feature-emoji {
  font-size: 2rem;
  flex-shrink: 0;
}

.launch-feature h4 {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-heritage-gold);
}

.launch-feature p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.stats-note {
  background: rgba(255, 255, 255, 0.12);
  padding: var(--space-lg);
  border-radius: 25px; /* Pill-shaped for consistency */
  backdrop-filter: blur(10px);
  border: 2px solid var(--color-heritage-gold);
}

.stats-note p {
  margin: 0;
  font-style: italic;
  opacity: 0.9;
  color: #ecf0f1;
}

/* Stats Mode Styles */
.stats-mode .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.12);
  padding: var(--space-xl);
  border-radius: 25px; /* Pill-shaped for consistency */
  backdrop-filter: blur(10px);
  transition: transform var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.stat-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.18);
}

.stat-number {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  color: var(--color-heritage-gold);
  margin-bottom: var(--space-sm);
  display: block;
}

.stat-label {
  font-size: var(--text-lg);
  opacity: 0.9;
  font-weight: 500;
}

/* Community Highlights (for future use) */
.community-highlights {
  margin-top: var(--space-2xl);
}

.community-highlights h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
  margin: 0 0 var(--space-xl) 0;
  color: var(--color-heritage-gold);
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.highlight-item {
  background: rgba(255, 255, 255, 0.12);
  padding: var(--space-xl);
  border-radius: 25px; /* Pill-shaped for consistency */
  backdrop-filter: blur(10px);
  text-align: center;
  transition: transform var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.highlight-item:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.18);
}

.highlight-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

.highlight-item h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin: 0 0 var(--space-sm) 0;
  color: var(--color-heritage-gold);
}

.highlight-item p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .launch-mode .launch-content {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .launch-message {
    text-align: center;
  }

  .stats-mode .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .highlights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .community-section {
    padding: var(--space-xl) var(--space-md);
  }

  .launch-features {
    gap: var(--space-md);
  }

  .launch-feature {
    padding: var(--space-md);
  }

  .stats-mode .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: var(--space-lg);
  }
}

/* Dark mode support */
.dark-mode .community-section {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.dark-mode .launch-feature,
.dark-mode .stat-item,
.dark-mode .highlight-item,
.dark-mode .stats-note {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .community-section {
    background: #000;
    color: #fff;
  }

  .launch-feature,
  .stat-item,
  .highlight-item,
  .stats-note {
    background: #333;
    border: 2px solid #fff;
  }
}

/* Online Community Section Styles */
.community-online-section {
  margin-top: var(--space-2xl);
  padding: var(--space-xl);
  background: rgba(255, 255, 255, 0.12);
  border-radius: 25px; /* Pill-shaped for consistency */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.online-header {
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.community-online-counter {
  background: linear-gradient(135deg, var(--color-heritage-gold), var(--color-warm-amber));
  color: var(--color-text-primary);
  padding: var(--space-sm) var(--space-lg);
  border-radius: 50px; /* Perfect pill shape */
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.community-online-users {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20px; /* Pill-shaped for consistency */
  padding: var(--space-lg);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.community-online-users .users-header h4 {
  color: var(--color-light);
  margin-bottom: var(--space-md);
  font-size: var(--text-lg);
}

.community-online-users .online-user-item {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition-normal);
}

.community-online-users .online-user-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.community-online-users .user-name {
  color: var(--color-light);
  font-weight: 500;
}

.community-online-users .presence-text {
  color: var(--color-accent-emerald);
  font-weight: 500;
}

/* Mobile responsive adjustments for online section */
@media (max-width: 768px) {
  .community-online-section {
    padding: var(--space-md);
    margin-top: var(--space-lg);
  }

  .community-online-counter {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--text-sm);
  }

  .community-online-users {
    padding: var(--space-md);
  }
}
